#include "event.h"
#include <errno.h>

#if !defined(_WIN32)
#include <sys/time.h>
#include <time.h>
#endif

int event_create(event_t* event)
{
    if (!event)
        return -1;
        
#if defined(_WIN32)
    *event = CreateEvent(NULL, TRUE, FALSE, NULL);
    return *event ? 0 : -1;
#else
    int ret = pthread_mutex_init(&event->mutex, NULL);
    if (ret != 0)
        return ret;
        
    ret = pthread_cond_init(&event->cond, NULL);
    if (ret != 0) {
        pthread_mutex_destroy(&event->mutex);
        return ret;
    }
    
    event->signaled = 0;
    return 0;
#endif
}

int event_destroy(event_t* event)
{
    if (!event)
        return -1;
        
#if defined(_WIN32)
    return CloseHandle(*event) ? 0 : -1;
#else
    pthread_cond_destroy(&event->cond);
    pthread_mutex_destroy(&event->mutex);
    return 0;
#endif
}

int event_wait(event_t* event)
{
    if (!event)
        return -1;
        
#if defined(_WIN32)
    return WaitForSingleObject(*event, INFINITE) == WAIT_OBJECT_0 ? 0 : -1;
#else
    pthread_mutex_lock(&event->mutex);
    while (!event->signaled) {
        pthread_cond_wait(&event->cond, &event->mutex);
    }
    pthread_mutex_unlock(&event->mutex);
    return 0;
#endif
}

int event_timewait(event_t* event, int timeout)
{
    if (!event)
        return -1;
        
#if defined(_WIN32)
    DWORD result = WaitForSingleObject(*event, timeout);
    if (result == WAIT_OBJECT_0)
        return 0;
    else if (result == WAIT_TIMEOUT)
        return ETIMEDOUT;
    else
        return -1;
#else
    struct timespec ts;
    struct timeval tv;
    
    gettimeofday(&tv, NULL);
    ts.tv_sec = tv.tv_sec + timeout / 1000;
    ts.tv_nsec = tv.tv_usec * 1000 + (timeout % 1000) * 1000000;
    if (ts.tv_nsec >= 1000000000) {
        ts.tv_sec++;
        ts.tv_nsec -= 1000000000;
    }
    
    pthread_mutex_lock(&event->mutex);
    int ret = 0;
    while (!event->signaled && ret == 0) {
        ret = pthread_cond_timedwait(&event->cond, &event->mutex, &ts);
    }
    pthread_mutex_unlock(&event->mutex);
    
    return ret;
#endif
}

int event_signal(event_t* event)
{
    if (!event)
        return -1;
        
#if defined(_WIN32)
    return SetEvent(*event) ? 0 : -1;
#else
    pthread_mutex_lock(&event->mutex);
    event->signaled = 1;
    pthread_cond_broadcast(&event->cond);
    pthread_mutex_unlock(&event->mutex);
    return 0;
#endif
}

int event_reset(event_t* event)
{
    if (!event)
        return -1;
        
#if defined(_WIN32)
    return ResetEvent(*event) ? 0 : -1;
#else
    pthread_mutex_lock(&event->mutex);
    event->signaled = 0;
    pthread_mutex_unlock(&event->mutex);
    return 0;
#endif
}
