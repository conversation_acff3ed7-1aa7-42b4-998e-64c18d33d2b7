#ifndef _ip_route_h_
#define _ip_route_h_

#ifdef __cplusplus
extern "C" {
#endif

// Get local IP address for routing to the specified destination
// @param[in] destination destination IP address
// @param[out] local local IP address buffer (should be at least SOCKET_ADDRLEN bytes)
// @return 0-ok, other-error
int ip_route_get(const char* destination, char* local);

#ifdef __cplusplus
}
#endif

#endif /* !_ip_route_h_ */
