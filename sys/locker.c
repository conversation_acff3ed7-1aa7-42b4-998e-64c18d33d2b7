#include "locker.h"

int locker_create(locker_t* locker)
{
    if (!locker)
        return -1;
        
#if defined(_WIN32)
    InitializeCriticalSection(locker);
    return 0;
#else
    return pthread_mutex_init(locker, NULL);
#endif
}

int locker_destroy(locker_t* locker)
{
    if (!locker)
        return -1;
        
#if defined(_WIN32)
    DeleteCriticalSection(locker);
    return 0;
#else
    return pthread_mutex_destroy(locker);
#endif
}

int locker_lock(locker_t* locker)
{
    if (!locker)
        return -1;
        
#if defined(_WIN32)
    EnterCriticalSection(locker);
    return 0;
#else
    return pthread_mutex_lock(locker);
#endif
}

int locker_unlock(locker_t* locker)
{
    if (!locker)
        return -1;
        
#if defined(_WIN32)
    LeaveCriticalSection(locker);
    return 0;
#else
    return pthread_mutex_unlock(locker);
#endif
}

int locker_trylock(locker_t* locker)
{
    if (!locker)
        return -1;
        
#if defined(_WIN32)
    return TryEnterCriticalSection(locker) ? 0 : -1;
#else
    return pthread_mutex_trylock(locker);
#endif
}
