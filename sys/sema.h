#ifndef _sema_h_
#define _sema_h_

#ifdef __cplusplus
extern "C" {
#endif

#if defined(_WIN32)
#include <windows.h>
typedef HANDLE sema_t;
#else
#include <semaphore.h>
typedef sem_t sema_t;
#endif

// Semaphore functions
int sema_create(sema_t* sema, const char* name, int value);
int sema_destroy(sema_t* sema);
int sema_open(sema_t* sema, const char* name);
int sema_wait(sema_t* sema);
int sema_timewait(sema_t* sema, int timeout);
int sema_trywait(sema_t* sema);
int sema_post(sema_t* sema);

#ifdef __cplusplus
}
#endif

#endif /* !_sema_h_ */
