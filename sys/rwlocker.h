#ifndef _rwlocker_h_
#define _rwlocker_h_

#ifdef __cplusplus
extern "C" {
#endif

#if defined(_WIN32)
#include <windows.h>
typedef SRWLOCK rwlocker_t;
#else
#include <pthread.h>
typedef pthread_rwlock_t rwlocker_t;
#endif

// Read-write locker functions
int rwlocker_create(rwlocker_t* locker);
int rwlocker_destroy(rwlocker_t* locker);
int rwlocker_rdlock(rwlocker_t* locker);
int rwlocker_rdunlock(rwlocker_t* locker);
int rwlocker_tryrdlock(rwlocker_t* locker);
int rwlocker_wrlock(rwlocker_t* locker);
int rwlocker_wrunlock(rwlocker_t* locker);
int rwlocker_trywrlock(rwlocker_t* locker);

#ifdef __cplusplus
}
#endif

#endif /* !_rwlocker_h_ */
