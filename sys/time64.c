#include "time64.h"
#include <time.h>
#include <sys/time.h>
#include <string.h>
#include <stdio.h>

time64_t time64_now(void)
{
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (time64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

int time64_utc(time64_t time, struct tm64* tm64)
{
    if (!tm64)
        return -1;
        
    time_t t = (time_t)(time / 1000);
    struct tm* tm = gmtime(&t);
    if (!tm)
        return -1;
        
    tm64->year = tm->tm_year + 1900;
    tm64->month = tm->tm_mon + 1;
    tm64->wday = tm->tm_wday;
    tm64->day = tm->tm_mday;
    tm64->hour = tm->tm_hour;
    tm64->minute = tm->tm_min;
    tm64->second = tm->tm_sec;
    tm64->millisecond = (int)(time % 1000);
    
    return 0;
}

int time64_local(time64_t time, struct tm64* tm64)
{
    if (!tm64)
        return -1;
        
    time_t t = (time_t)(time / 1000);
    struct tm* tm = localtime(&t);
    if (!tm)
        return -1;
        
    tm64->year = tm->tm_year + 1900;
    tm64->month = tm->tm_mon + 1;
    tm64->wday = tm->tm_wday;
    tm64->day = tm->tm_mday;
    tm64->hour = tm->tm_hour;
    tm64->minute = tm->tm_min;
    tm64->second = tm->tm_sec;
    tm64->millisecond = (int)(time % 1000);
    
    return 0;
}

time64_t time64_from(const char* format, const char* src)
{
    // Simple implementation - just parse basic format
    if (!format || !src)
        return 0;
        
    struct tm tm = {0};
    int ms = 0;
    
    // Try to parse common format: YYYY-MM-DD HH:MM:SS
    if (sscanf(src, "%d-%d-%d %d:%d:%d", 
               &tm.tm_year, &tm.tm_mon, &tm.tm_mday,
               &tm.tm_hour, &tm.tm_min, &tm.tm_sec) == 6) {
        tm.tm_year -= 1900;
        tm.tm_mon -= 1;
        time_t t = mktime(&tm);
        if (t != -1) {
            return (time64_t)t * 1000 + ms;
        }
    }
    
    return 0;
}

int time64_format(time64_t time, const char* format, char* str)
{
    if (!format || !str)
        return -1;
        
    struct tm64 tm64;
    if (time64_utc(time, &tm64) != 0)
        return -1;
        
    // Simple format implementation
    if (strcmp(format, "%Y-%M-%D %h:%m:%s") == 0) {
        sprintf(str, "%04d-%02d-%02d %02d:%02d:%02d",
                tm64.year, tm64.month, tm64.day,
                tm64.hour, tm64.minute, tm64.second);
        return 0;
    } else if (strcmp(format, "%04Y-%02M-%02D %02h:%02m:%02s") == 0) {
        sprintf(str, "%04d-%02d-%02d %02d:%02d:%02d",
                tm64.year, tm64.month, tm64.day,
                tm64.hour, tm64.minute, tm64.second);
        return 0;
    }
    
    // Default format
    sprintf(str, "%04d-%02d-%02d %02d:%02d:%02d.%03d",
            tm64.year, tm64.month, tm64.day,
            tm64.hour, tm64.minute, tm64.second, tm64.millisecond);
    
    return 0;
}
