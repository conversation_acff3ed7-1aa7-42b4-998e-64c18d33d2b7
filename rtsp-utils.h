#ifndef _rtsp_utils_h_
#define _rtsp_utils_h_

#include <stdint.h>
#include <stddef.h>
#include <string>
#include <memory>

#ifdef __cplusplus
extern "C" {
#endif

// Basic type definitions
#ifndef SOCKET_ADDRLEN
#define SOCKET_ADDRLEN 64
#endif

// String utility functions
int strstartswith(const char* str, const char* prefix);
int strendswith(const char* str, const char* suffix);
// const char* path_basename(const char* path);  // defined in sys/path.h
uint64_t ntp64_now(void);
// uint64_t system_time(void);  // defined in sys/system.h

// URI parsing functions (defined in sys/uri-parse.h and sys/urlcodec.h)
// struct uri_t* uri_parse(const char* uri, int len);
// void uri_free(struct uri_t* uri);
// int url_decode(const char* source, int srcBytes, char* target, int tgtBytes);

#ifdef __cplusplus
}

// C++ classes and utilities
// ThreadLocker and AutoThreadLocker are defined in sys/sync.hpp

// Path operations namespace
namespace path {
    std::string join(const char* path1, const char* path2);
}

#endif

#endif /* !_rtsp_utils_h_ */