# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/rtspSRV

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/rtspSRV/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/rtp.dir/all
all: CMakeFiles/rtsp-server-test.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/rtp.dir/clean
clean: CMakeFiles/rtsp-server-test.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/rtp.dir

# All Build rule for target.
CMakeFiles/rtp.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32 "Built target rtp"
.PHONY : CMakeFiles/rtp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/rtp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/rtspSRV/build/CMakeFiles 32
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/rtp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/rtspSRV/build/CMakeFiles 0
.PHONY : CMakeFiles/rtp.dir/rule

# Convenience name for target.
rtp: CMakeFiles/rtp.dir/rule
.PHONY : rtp

# clean rule for target.
CMakeFiles/rtp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/clean
.PHONY : CMakeFiles/rtp.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/rtsp-server-test.dir

# All Build rule for target.
CMakeFiles/rtsp-server-test.dir/all: CMakeFiles/rtp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49 "Built target rtsp-server-test"
.PHONY : CMakeFiles/rtsp-server-test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/rtsp-server-test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/rtspSRV/build/CMakeFiles 49
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/rtsp-server-test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/rtspSRV/build/CMakeFiles 0
.PHONY : CMakeFiles/rtsp-server-test.dir/rule

# Convenience name for target.
rtsp-server-test: CMakeFiles/rtsp-server-test.dir/rule
.PHONY : rtsp-server-test

# clean rule for target.
CMakeFiles/rtsp-server-test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/clean
.PHONY : CMakeFiles/rtsp-server-test.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

