/usr/bin/ar qc librtp.a CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o CMakeFiles/rtp.dir/librtp/source/rtcp.c.o CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o CMakeFiles/rtp.dir/librtp/source/rtp.c.o
/usr/bin/ranlib librtp.a
