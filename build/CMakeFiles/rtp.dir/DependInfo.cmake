
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h264-bitstream.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h264-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h264-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h265-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-h265-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-payload-helper.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-payload.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-ps-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-ts-pack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-ts-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/payload/rtp-unpack.c" "CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-app.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-bye.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-interval.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-psfb.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-rr.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-rtpfb.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-sdec.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-sr.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp-xr.c" "CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtcp.c" "CMakeFiles/rtp.dir/librtp/source/rtcp.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtcp.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-demuxer.c" "CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-member-list.c" "CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-member.c" "CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-packet.c" "CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-profile.c" "CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-queue.c" "CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-ssrc.c" "CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp-time.c" "CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o.d"
  "/home/<USER>/rtspSRV/librtp/source/rtp.c" "CMakeFiles/rtp.dir/librtp/source/rtp.c.o" "gcc" "CMakeFiles/rtp.dir/librtp/source/rtp.c.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
